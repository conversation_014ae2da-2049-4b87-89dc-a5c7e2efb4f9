import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { AuthWrapper } from "@/components/auth-wrapper"
import { QueryProvider } from "@/components/query-provider"
import { Toaster } from "@/components/ui/sonner"

const inter = Inter({ subsets: ["latin", "vietnamese"] })

export const metadata: Metadata = {
  title: "Ứng dụng Quản lý Thực phẩm",
  description: "Ứng dụng di động quản lý thực phẩm và lập kế hoạch bữa ăn",
  generator: "v0.dev",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="vi">
      <body className={inter.className}>
        <QueryProvider>
          {/* AuthWrapper is a client component but now safely nested
              inside the client-side QueryProvider */}
          <AuthWrapper>{children}</AuthWrapper>
          <Toaster />
        </QueryProvider>
      </body>
    </html>
  )
}
