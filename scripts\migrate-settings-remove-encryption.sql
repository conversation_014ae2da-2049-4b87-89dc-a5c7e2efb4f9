-- Migration to remove encryption from user_settings table
-- This script updates the table structure to store API keys in plain text

-- Add the new plain text column
ALTER TABLE user_settings ADD COLUMN IF NOT EXISTS gemini_api_key TEXT;

-- For existing users with encrypted keys, they will need to re-enter their API keys
-- We cannot decrypt the existing keys without the session-stored encryption keys

-- Remove the old encrypted columns
ALTER TABLE user_settings DROP COLUMN IF EXISTS gemini_api_key_encrypted;
ALTER TABLE user_settings DROP COLUMN IF EXISTS encryption_key_id;

-- Update the table comment
COMMENT ON TABLE user_settings IS 'User settings including API keys and preferences';
COMMENT ON COLUMN user_settings.gemini_api_key IS 'Gemini API key stored in plain text';
