/**
 * Test script to verify the upsert fix for user_settings table
 * This script tests the upsert operations that were causing the duplicate key constraint error
 */

const { createClient } = require('@supabase/supabase-js')

// Mock environment variables for testing
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'your-supabase-url'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key'

if (!supabaseUrl || !supabaseAnonKey || supabaseUrl === 'your-supabase-url') {
  console.log('⚠️  Please set NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY environment variables')
  console.log('   This test requires a real Supabase connection to verify the fix')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testUpsertFix() {
  console.log('🧪 Testing upsert fix for user_settings table...\n')

  try {
    // Get current user (you need to be authenticated)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      console.log('❌ Authentication required. Please log in to your app first.')
      console.log('   This test needs to run with an authenticated user session.')
      return
    }

    console.log(`✅ Authenticated as user: ${user.id}`)

    // Test 1: First upsert (should create new record)
    console.log('\n📝 Test 1: First upsert (create new record)...')
    const { error: error1 } = await supabase.from("user_settings").upsert(
      {
        user_id: user.id,
        gemini_api_key: 'test-api-key-1',
        preferences: { theme: 'dark' },
      },
      {
        onConflict: "user_id",
      }
    )

    if (error1) {
      console.log('❌ First upsert failed:', error1.message)
      return
    }
    console.log('✅ First upsert successful')

    // Test 2: Second upsert (should update existing record)
    console.log('\n📝 Test 2: Second upsert (update existing record)...')
    const { error: error2 } = await supabase.from("user_settings").upsert(
      {
        user_id: user.id,
        gemini_api_key: 'test-api-key-2',
        preferences: { theme: 'light', notifications: true },
      },
      {
        onConflict: "user_id",
      }
    )

    if (error2) {
      console.log('❌ Second upsert failed:', error2.message)
      return
    }
    console.log('✅ Second upsert successful')

    // Test 3: Third upsert with null API key (should update existing record)
    console.log('\n📝 Test 3: Third upsert with null API key...')
    const { error: error3 } = await supabase.from("user_settings").upsert(
      {
        user_id: user.id,
        gemini_api_key: null,
        preferences: { theme: 'light', notifications: false },
      },
      {
        onConflict: "user_id",
      }
    )

    if (error3) {
      console.log('❌ Third upsert failed:', error3.message)
      return
    }
    console.log('✅ Third upsert successful')

    // Verify final state
    console.log('\n🔍 Verifying final state...')
    const { data: finalData, error: selectError } = await supabase
      .from("user_settings")
      .select("*")
      .eq("user_id", user.id)
      .single()

    if (selectError) {
      console.log('❌ Failed to fetch final state:', selectError.message)
      return
    }

    console.log('✅ Final state verified:')
    console.log('   - API Key:', finalData.gemini_api_key || 'null')
    console.log('   - Preferences:', JSON.stringify(finalData.preferences))
    console.log('   - Updated at:', finalData.updated_at)

    console.log('\n🎉 All tests passed! The upsert fix is working correctly.')
    console.log('   No more "duplicate key value violates unique constraint" errors!')

  } catch (error) {
    console.log('❌ Test failed with error:', error.message)
  }
}

// Run the test
testUpsertFix()
