-- Create categories and units tables for the food management app

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Units table
CREATE TABLE IF NOT EXISTS units (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  display_name TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, name)
);

-- Enable Row Level Security
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE units ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for categories
CREATE POLICY "Users can view their own categories" ON categories
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own categories" ON categories
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own categories" ON categories
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own categories" ON categories
  FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for units
CREATE POLICY "Users can view their own units" ON units
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own units" ON units
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own units" ON units
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own units" ON units
  FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_categories_user_id ON categories(user_id);
CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(user_id, name);
CREATE INDEX IF NOT EXISTS idx_units_user_id ON units(user_id);
CREATE INDEX IF NOT EXISTS idx_units_name ON units(user_id, name);

-- Function to seed default categories and units for new users
CREATE OR REPLACE FUNCTION seed_default_categories_units()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert default categories
  INSERT INTO categories (user_id, name, display_name) VALUES
    (NEW.id, 'fruits', 'Fruits'),
    (NEW.id, 'vegetables', 'Vegetables'),
    (NEW.id, 'dairy', 'Dairy'),
    (NEW.id, 'meat', 'Meat'),
    (NEW.id, 'grains', 'Grains'),
    (NEW.id, 'pantry', 'Pantry'),
    (NEW.id, 'frozen', 'Frozen'),
    (NEW.id, 'beverages', 'Beverages'),
    (NEW.id, 'snacks', 'Snacks'),
    (NEW.id, 'other', 'Other');

  -- Insert default units
  INSERT INTO units (user_id, name, display_name) VALUES
    (NEW.id, 'pieces', 'Pieces'),
    (NEW.id, 'lbs', 'Lbs'),
    (NEW.id, 'oz', 'Oz'),
    (NEW.id, 'kg', 'Kg'),
    (NEW.id, 'g', 'G'),
    (NEW.id, 'cups', 'Cups'),
    (NEW.id, 'liters', 'Liters'),
    (NEW.id, 'ml', 'Ml'),
    (NEW.id, 'cans', 'Cans'),
    (NEW.id, 'bottles', 'Bottles'),
    (NEW.id, 'packages', 'Packages');

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically seed default data for new users
DROP TRIGGER IF EXISTS trigger_seed_default_categories_units ON auth.users;
CREATE TRIGGER trigger_seed_default_categories_units
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION seed_default_categories_units();

-- Add comments for documentation
COMMENT ON TABLE categories IS 'User-specific food categories for organizing inventory items';
COMMENT ON TABLE units IS 'User-specific measurement units for inventory items';
COMMENT ON COLUMN categories.name IS 'Internal name used in code (lowercase, no spaces)';
COMMENT ON COLUMN categories.display_name IS 'Human-readable name shown in UI';
COMMENT ON COLUMN units.name IS 'Internal name used in code (lowercase, no spaces)';
COMMENT ON COLUMN units.display_name IS 'Human-readable name shown in UI';
