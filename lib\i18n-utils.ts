// Vietnamese text constants
export const COMMON_TEXT = {
  loading: "Đang tải...",
  save: "<PERSON><PERSON><PERSON>",
  cancel: "<PERSON><PERSON><PERSON>",
  delete: "<PERSON>ó<PERSON>",
  edit: "Chỉnh sửa",
  add: "Thêm",
  remove: "Gỡ bỏ",
  confirm: "<PERSON><PERSON><PERSON> nhận",
  close: "<PERSON>óng",
  back: "<PERSON>uay lại",
  next: "Tiế<PERSON> theo",
  previous: "Trước",
  search: "Tìm kiếm",
  filter: "Lọ<PERSON>",
  sort: "Sắp xếp",
  clear: "Xóa",
  refresh: "Làm mới",
  submit: "<PERSON>ửi",
  update: "Cập nhật",
  create: "Tạo",
  view: "Xem",
  settings: "Cài đặt",
  home: "Trang chủ",
  yes: "Có",
  no: "Không",
  ok: "Đồng ý",
  error: "Lỗi",
  success: "Thành công",
  warning: "Cảnh báo",
  info: "Thông tin"
};

export const NAVIGATION_TEXT = {
  home: "Trang chủ",
  inventory: "Kho",
  meals: "Bữa ăn",
  recipes: "Công thức",
  shopping: "Mua sắm",
  settings: "Cài đặt"
};

export const AUTH_TEXT = {
  signIn: "Đăng nhập",
  signUp: "Đăng ký",
  signOut: "Đăng xuất",
  email: "Email",
  password: "Mật khẩu",
  confirmPassword: "Xác nhận mật khẩu",
  forgotPassword: "Quên mật khẩu?",
  rememberMe: "Ghi nhớ đăng nhập",
  createAccount: "Tạo tài khoản",
  alreadyHaveAccount: "Đã có tài khoản?",
  dontHaveAccount: "Chưa có tài khoản?",
  signInWithGoogle: "Đăng nhập với Google",
  signInWithFacebook: "Đăng nhập với Facebook",
  resetPassword: "Đặt lại mật khẩu",
  checkEmail: "Kiểm tra email của bạn",
  invalidCredentials: "Thông tin đăng nhập không hợp lệ",
  accountCreated: "Tài khoản đã được tạo thành công",
  passwordReset: "Liên kết đặt lại mật khẩu đã được gửi",
  signedOut: "Đã đăng xuất thành công"
};

export const ERROR_TEXT = {
  general: "Đã xảy ra lỗi",
  networkError: "Lỗi mạng",
  serverError: "Lỗi máy chủ",
  notFound: "Không tìm thấy",
  unauthorized: "Không có quyền truy cập",
  forbidden: "Bị cấm",
  validationError: "Lỗi xác thực",
  requiredField: "Trường này là bắt buộc",
  invalidEmail: "Email không hợp lệ",
  invalidPassword: "Mật khẩu không hợp lệ",
  passwordTooShort: "Mật khẩu quá ngắn",
  passwordMismatch: "Mật khẩu không khớp",
  invalidDate: "Ngày không hợp lệ",
  invalidNumber: "Số không hợp lệ",
  invalidUrl: "URL không hợp lệ",
  fileTooLarge: "Tệp quá lớn",
  fileTypeNotSupported: "Loại tệp không được hỗ trợ",
  uploadFailed: "Tải lên thất bại",
  downloadFailed: "Tải xuống thất bại",
  saveFailed: "Lưu thất bại",
  loadFailed: "Tải thất bại",
  deleteFailed: "Xóa thất bại",
  updateFailed: "Cập nhật thất bại",
  createFailed: "Tạo thất bại",
  connectionTimeout: "Hết thời gian kết nối",
  requestTimeout: "Hết thời gian yêu cầu",
  tooManyRequests: "Quá nhiều yêu cầu",
  serviceUnavailable: "Dịch vụ không khả dụng",
  maintenanceMode: "Chế độ bảo trì",
  tryAgainLater: "Vui lòng thử lại sau",
  contactSupport: "Liên hệ hỗ trợ nếu vấn đề vẫn tiếp tục"
};

export const DASHBOARD_TEXT = {
  title: "Bảng điều khiển",
  welcome: "Chào mừng trở lại!",
  overview: "Tổng quan",
  quickStats: "Thống kê nhanh",
  recentActivity: "Hoạt động gần đây",
  upcomingMeals: "Bữa ăn sắp tới",
  expiringItems: "Thực phẩm sắp hết hạn",
  lowStock: "Sắp hết hàng",
  recipeSuggestions: "Gợi ý công thức",
  todaysMeals: "Bữa ăn hôm nay",
  inventoryOverview: "Tổng quan kho",
  noMealsPlanned: "Chưa có bữa ăn nào được lên kế hoạch cho hôm nay",
  noExpiringItems: "Không có thực phẩm nào sắp hết hạn",
  addMealPlan: "Thêm kế hoạch bữa ăn",
  viewInventory: "Xem kho",
  planMeals: "Lên kế hoạch bữa ăn"
};

export const INVENTORY_TEXT = {
  title: "Kho thực phẩm",
  addItem: "Thêm thực phẩm",
  editItem: "Chỉnh sửa thực phẩm",
  deleteItem: "Xóa thực phẩm",
  itemName: "Tên thực phẩm",
  quantity: "Số lượng",
  unit: "Đơn vị",
  category: "Danh mục",
  expirationDate: "Ngày hết hạn",
  addedDate: "Ngày thêm",
  status: "Trạng thái",
  fresh: "Tươi",
  expiring: "Sắp hết hạn",
  soon: "Sắp hết hạn",
  expired: "Hết hạn",
  noItems: "Không tìm thấy thực phẩm",
  noItemsEmpty: "Chưa có thực phẩm nào trong kho",
  searchPlaceholder: "Tìm kiếm thực phẩm...",
  filterByCategory: "Lọc theo danh mục",
  sortBy: "Sắp xếp theo",
  sortByName: "Tên",
  sortByDate: "Ngày",
  sortByExpiration: "Ngày hết hạn",
  itemAdded: "Đã thêm thực phẩm thành công",
  itemUpdated: "Đã cập nhật thực phẩm thành công",
  itemDeleted: "Đã xóa thực phẩm thành công",
  confirmDelete: "Bạn có chắc chắn muốn xóa thực phẩm này?",
  uploadImage: "Tải lên hình ảnh",
  takePhoto: "Chụp ảnh",
  removeImage: "Gỡ bỏ hình ảnh",
  loadingInventory: "Đang tải kho...",
  errorLoadingInventory: "Lỗi khi tải kho",
  unknownError: "Đã xảy ra lỗi không xác định.",
  tryAdjustFilter: "Thử điều chỉnh tìm kiếm hoặc bộ lọc của bạn",
  startByAdding: "Bắt đầu bằng cách thêm một số thực phẩm vào kho của bạn",
  addFirstItem: "Thêm thực phẩm đầu tiên",
  all: "Tất cả",
  expires: "Hết hạn:",
  noImage: "Không có hình",
  failedToDelete: "Không thể xóa thực phẩm. Vui lòng thử lại.",
  failedToRefresh: "Không thể làm mới kho."
};

export const RECIPES_TEXT = {
  title: "Công thức nấu ăn",
  addRecipe: "Thêm công thức",
  editRecipe: "Chỉnh sửa công thức",
  deleteRecipe: "Xóa công thức",
  recipeName: "Tên công thức",
  description: "Mô tả",
  ingredients: "Nguyên liệu",
  instructions: "Hướng dẫn",
  prepTime: "Thời gian chuẩn bị",
  cookTime: "Thời gian nấu",
  servings: "Khẩu phần",
  difficulty: "Độ khó",
  easy: "Dễ",
  medium: "Trung bình",
  hard: "Khó",
  cuisine: "Ẩm thực",
  tags: "Thẻ",
  noRecipes: "Chưa có công thức nào",
  searchRecipes: "Tìm kiếm công thức...",
  filterByCuisine: "Lọc theo ẩm thực",
  filterByDifficulty: "Lọc theo độ khó",
  recipeAdded: "Đã thêm công thức thành công",
  recipeUpdated: "Đã cập nhật công thức thành công",
  recipeDeleted: "Đã xóa công thức thành công",
  confirmDeleteRecipe: "Bạn có chắc chắn muốn xóa công thức này?",
  addIngredient: "Thêm nguyên liệu",
  removeIngredient: "Gỡ bỏ nguyên liệu",
  addStep: "Thêm bước",
  removeStep: "Gỡ bỏ bước",
  generateWithAI: "Tạo bằng AI",
  aiSuggestions: "Gợi ý từ AI",
  nutritionInfo: "Thông tin dinh dưỡng",
  calories: "Calo",
  protein: "Protein",
  carbs: "Carbohydrate",
  fat: "Chất béo",
  fiber: "Chất xơ"
};

export const SETTINGS_TEXT = {
  title: "Cài đặt",
  profile: "Hồ sơ",
  preferences: "Tùy chọn",
  notifications: "Thông báo",
  privacy: "Quyền riêng tư",
  about: "Về ứng dụng",
  language: "Ngôn ngữ",
  theme: "Giao diện",
  units: "Đơn vị",
  categories: "Danh mục",
  backup: "Sao lưu",
  restore: "Khôi phục",
  export: "Xuất dữ liệu",
  import: "Nhập dữ liệu",
  deleteAccount: "Xóa tài khoản",
  signOut: "Đăng xuất"
};

// Re-export Vietnamese formatting utilities for convenience
export {
  formatVietnameseDate,
  formatVietnameseTime,
  formatVietnameseDateTime,
  formatVietnameseNumber,
  formatVietnameseCurrency,
  formatVietnamesePercentage,
  formatVietnameseRelativeTime,
  formatVietnameseFileSize,
  formatVietnameseDuration,
  formatVietnameseQuantity,
  getVietnameseDayName,
  getVietnameseMonthName,
  isToday,
  isTomorrow,
  getCurrentVietnameseTime,
  VIETNAMESE_LOCALE,
  VIETNAMESE_TIMEZONE,
  VIETNAMESE_CURRENCY
} from './vietnamese-formatting';

// Vietnamese-specific text utilities
export function capitalizeVietnamese(text: string): string {
  return text.charAt(0).toUpperCase() + text.slice(1);
}

export function pluralizeVietnamese(count: number, singular: string, plural?: string): string {
  // Vietnamese doesn't have plural forms like English, but we can add quantity indicators
  if (count === 1) {
    return `1 ${singular}`;
  }
  return `${count} ${plural || singular}`;
}
